<?php $__env->startSection('title', 'Quản lý gói dịch vụ'); ?>
<?php $__env->startSection('page-title', 'Quản lý gói dịch vụ'); ?>

<?php $__env->startSection('content'); ?>
<div class="row g-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <div class="icon-wrapper me-3" style="width: 50px; height: 50px; background: var(--success-gradient); border-radius: 15px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-box fa-lg text-white"></i>
                        </div>
                        <div>
                            <h5 class="mb-0 fw-bold">Danh sách gói dịch vụ</h5>
                            <small class="text-muted">Quản lý các gói dịch vụ và sản phẩm</small>
                        </div>
                    </div>
                    <a href="<?php echo e(route('admin.service-packages.create')); ?>" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i>
                        Thêm gói dịch vụ
                    </a>
                </div>
            </div>
    
    <div class="card-body">
        <!-- Advanced Filters Card -->
        <div class="card border-0 shadow-sm mb-4" style="background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="fw-semibold text-muted mb-0">
                        <i class="fas fa-filter me-1"></i>
                        Bộ lọc nâng cao
                    </h6>
                    <?php if(request()->hasAny(['search', 'category_id', 'status', 'date_from', 'date_to'])): ?>
                        <div class="badge bg-primary">
                            <?php echo e(collect(request()->only(['search', 'category_id', 'status', 'date_from', 'date_to']))->filter()->count()); ?> bộ lọc đang áp dụng
                        </div>
                    <?php endif; ?>
                </div>
                <form method="GET">
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label small text-muted fw-semibold">
                                <i class="fas fa-search me-1"></i>Tìm kiếm
                            </label>
                            <div class="input-group">
                                <span class="input-group-text bg-white border-end-0">
                                    <i class="fas fa-search text-muted"></i>
                                </span>
                                <input type="text" 
                                       name="search" 
                                       class="form-control border-start-0 ps-0" 
                                       placeholder="Tên gói, loại tài khoản..."
                                       value="<?php echo e(request('search')); ?>">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label small text-muted fw-semibold">
                                <i class="fas fa-tags me-1"></i>Danh mục
                            </label>
                            <select name="category_id" class="form-select">
                                <option value="">Tất cả danh mục</option>
                                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($category->id); ?>" 
                                            <?php echo e(request('category_id') == $category->id ? 'selected' : ''); ?>>
                                        <?php echo e($category->name); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label small text-muted fw-semibold">
                                <i class="fas fa-toggle-on me-1"></i>Trạng thái
                            </label>
                            <select name="status" class="form-select">
                                <option value="">Tất cả trạng thái</option>
                                <option value="active" <?php echo e(request('status') === 'active' ? 'selected' : ''); ?>>
                                    <i class="fas fa-check-circle text-success"></i> Hoạt động
                                </option>
                                <option value="inactive" <?php echo e(request('status') === 'inactive' ? 'selected' : ''); ?>>
                                    <i class="fas fa-pause-circle text-warning"></i> Tạm dừng
                                </option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label small text-muted fw-semibold">
                                <i class="fas fa-calendar-alt me-1"></i>Từ ngày
                            </label>
                            <input type="date" 
                                   name="date_from" 
                                   class="form-control"
                                   value="<?php echo e(request('date_from')); ?>"
                                   max="<?php echo e(now()->format('Y-m-d')); ?>">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label small text-muted fw-semibold">
                                <i class="fas fa-calendar-check me-1"></i>Đến ngày
                            </label>
                            <input type="date" 
                                   name="date_to" 
                                   class="form-control"
                                   value="<?php echo e(request('date_to')); ?>"
                                   max="<?php echo e(now()->format('Y-m-d')); ?>">
                        </div>
                        <div class="col-md-1 d-flex align-items-end">
                            <div class="d-flex flex-column gap-2 w-100">
                                <button type="submit" class="btn btn-primary d-flex align-items-center justify-content-center" title="Áp dụng bộ lọc">
                                    <i class="fas fa-search"></i>
                                </button>
                                <?php if(request()->hasAny(['search', 'category_id', 'status', 'date_from', 'date_to'])): ?>
                                    <a href="<?php echo e(route('admin.service-packages.index')); ?>" 
                                       class="btn btn-outline-secondary d-flex align-items-center justify-content-center"
                                       title="Xóa tất cả bộ lọc">
                                        <i class="fas fa-times"></i>
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Filter Buttons -->
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="d-flex flex-wrap gap-2">
                                <span class="small text-muted fw-semibold">Lọc nhanh:</span>
                                <a href="<?php echo e(route('admin.service-packages.index', ['date_from' => now()->format('Y-m-d')])); ?>" 
                                   class="btn btn-sm <?php echo e(request('date_from') === now()->format('Y-m-d') ? 'btn-primary' : 'btn-outline-primary'); ?>">
                                    <i class="fas fa-calendar-day me-1"></i>Hôm nay
                                </a>
                                <a href="<?php echo e(route('admin.service-packages.index', ['date_from' => now()->startOfWeek()->format('Y-m-d'), 'date_to' => now()->endOfWeek()->format('Y-m-d')])); ?>" 
                                   class="btn btn-sm <?php echo e(request('date_from') === now()->startOfWeek()->format('Y-m-d') && request('date_to') === now()->endOfWeek()->format('Y-m-d') ? 'btn-primary' : 'btn-outline-primary'); ?>">
                                    <i class="fas fa-calendar-week me-1"></i>Tuần này
                                </a>
                                <a href="<?php echo e(route('admin.service-packages.index', ['date_from' => now()->startOfMonth()->format('Y-m-d'), 'date_to' => now()->endOfMonth()->format('Y-m-d')])); ?>" 
                                   class="btn btn-sm <?php echo e(request('date_from') === now()->startOfMonth()->format('Y-m-d') && request('date_to') === now()->endOfMonth()->format('Y-m-d') ? 'btn-primary' : 'btn-outline-primary'); ?>">
                                    <i class="fas fa-calendar me-1"></i>Tháng này
                                </a>
                                <a href="<?php echo e(route('admin.service-packages.index', ['status' => 'active'])); ?>" 
                                   class="btn btn-sm <?php echo e(request('status') === 'active' ? 'btn-success' : 'btn-outline-success'); ?>">
                                    <i class="fas fa-check-circle me-1"></i>Đang hoạt động
                                </a>
                                <a href="<?php echo e(route('admin.service-packages.index', ['status' => 'inactive'])); ?>" 
                                   class="btn btn-sm <?php echo e(request('status') === 'inactive' ? 'btn-warning' : 'btn-outline-warning'); ?>">
                                    <i class="fas fa-pause-circle me-1"></i>Tạm dừng
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Results Info with Enhanced Statistics -->
        <div class="row mb-3">
            <div class="col-md-8">
                <div class="d-flex align-items-center gap-3">
                    <div class="text-muted">
                        <i class="fas fa-list me-1"></i>
                        Hiển thị <strong><?php echo e($servicePackages->firstItem() ?? 0); ?> - <?php echo e($servicePackages->lastItem() ?? 0); ?></strong> 
                        trong tổng số <strong><?php echo e($servicePackages->total()); ?></strong> gói dịch vụ
                    </div>
                    <?php if($servicePackages->total() > 0): ?>
                        <div class="vr"></div>
                        <div class="d-flex gap-3">
                            <div class="small">
                                <i class="fas fa-check-circle text-success me-1"></i>
                                <span class="text-success fw-semibold"><?php echo e($servicePackages->where('is_active', true)->count()); ?></span> 
                                <span class="text-muted">hoạt động</span>
                            </div>
                            <div class="small">
                                <i class="fas fa-pause-circle text-warning me-1"></i>
                                <span class="text-warning fw-semibold"><?php echo e($servicePackages->where('is_active', false)->count()); ?></span> 
                                <span class="text-muted">tạm dừng</span>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <div class="col-md-4 text-end">
                <?php if(request()->hasAny(['search', 'category_id', 'status', 'date_from', 'date_to'])): ?>
                    <div class="small text-muted">
                        <i class="fas fa-filter me-1"></i>
                        Đã áp dụng <?php echo e(collect(request()->only(['search', 'category_id', 'status', 'date_from', 'date_to']))->filter()->count()); ?> bộ lọc
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Active Filters Summary -->
        <?php if(request()->hasAny(['search', 'category_id', 'status', 'date_from', 'date_to'])): ?>
            <div class="alert alert-info border-0 shadow-sm mb-3" style="background: linear-gradient(135deg, rgba(13, 202, 240, 0.1) 0%, rgba(13, 110, 253, 0.1) 100%);">
                <div class="d-flex align-items-center">
                    <i class="fas fa-info-circle text-info me-2"></i>
                    <div class="flex-grow-1">
                        <strong class="text-info">Bộ lọc đang áp dụng:</strong>
                        <div class="d-flex flex-wrap gap-2 mt-2">
                            <?php if(request('search')): ?>
                                <span class="badge bg-primary">
                                    <i class="fas fa-search me-1"></i>
                                    "<?php echo e(request('search')); ?>"
                                </span>
                            <?php endif; ?>
                            <?php if(request('category_id')): ?>
                                <span class="badge bg-secondary">
                                    <i class="fas fa-tags me-1"></i>
                                    <?php echo e($categories->find(request('category_id'))->name ?? 'Danh mục không xác định'); ?>

                                </span>
                            <?php endif; ?>
                            <?php if(request('status')): ?>
                                <span class="badge <?php echo e(request('status') === 'active' ? 'bg-success' : 'bg-warning'); ?>">
                                    <i class="fas <?php echo e(request('status') === 'active' ? 'fa-check-circle' : 'fa-pause-circle'); ?> me-1"></i>
                                    <?php echo e(request('status') === 'active' ? 'Hoạt động' : 'Tạm dừng'); ?>

                                </span>
                            <?php endif; ?>
                            <?php if(request('date_from')): ?>
                                <span class="badge bg-info">
                                    <i class="fas fa-calendar-alt me-1"></i>
                                    Từ <?php echo e(\Carbon\Carbon::parse(request('date_from'))->format('d/m/Y')); ?>

                                </span>
                            <?php endif; ?>
                            <?php if(request('date_to')): ?>
                                <span class="badge bg-info">
                                    <i class="fas fa-calendar-check me-1"></i>
                                    Đến <?php echo e(\Carbon\Carbon::parse(request('date_to'))->format('d/m/Y')); ?>

                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <a href="<?php echo e(route('admin.service-packages.index')); ?>" class="btn btn-sm btn-outline-info">
                        <i class="fas fa-times me-1"></i>
                        Xóa tất cả
                    </a>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Service Packages Table -->
        <?php if($servicePackages->count() > 0): ?>
            <div class="table-container horizontal-scroll-container">
                <div class="table-responsive-enhanced">
                    <table class="table mb-0 table-fixed-layout table-min-width-large">
                    <thead>
                        <tr>
                            <th>
                                <i class="fas fa-box me-2"></i>
                                Thông tin gói
                            </th>
                            <th>
                                <i class="fas fa-tag me-2"></i>
                                Danh mục
                            </th>
                            <th>
                                <i class="fas fa-user-circle me-2"></i>
                                Loại tài khoản
                            </th>
                            <th>
                                <i class="fas fa-money-bill-wave me-2"></i>
                                Giá & Lợi nhuận
                            </th>
                            <th>
                                <i class="fas fa-users me-2"></i>
                                Khách hàng
                            </th>
                            <th>
                                <i class="fas fa-toggle-on me-2"></i>
                                Trạng thái
                            </th>
                            <th class="text-center table-action-column">
                                <i class="fas fa-cogs me-2"></i>
                                Thao tác
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $servicePackages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $package): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-start">
                                        <div class="icon-wrapper me-3" style="width: 40px; height: 40px; background: var(--success-gradient); border-radius: 10px; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                                            <i class="fas fa-box text-white"></i>
                                        </div>
                                        <div>
                                            <div class="fw-bold"><?php echo e($package->name); ?></div>
                                            <?php if($package->description): ?>
                                                <small class="text-muted d-block"><?php echo e(Str::limit($package->description, 60)); ?></small>
                                            <?php endif; ?>
                                            <div class="mt-1">
                                                <span class="badge bg-info">
                                                    <i class="fas fa-clock me-1"></i>
                                                    <?php echo e($package->default_duration_days); ?> ngày
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-primary"><?php echo e($package->category->name); ?></span>
                                </td>
                                <td>
                                    <span class="badge bg-secondary"><?php echo e($package->account_type); ?></span>
                                </td>
                                <td>
                                    <div>
                                        <div class="fw-bold text-success mb-1">
                                            <i class="fas fa-money-bill-wave me-1"></i>
                                            <?php echo e(number_format($package->price)); ?>đ
                                        </div>
                                        <?php if($package->cost_price): ?>
                                            <small class="text-muted d-block">
                                                <i class="fas fa-shopping-cart me-1"></i>
                                                Nhập: <?php echo e(number_format($package->cost_price)); ?>đ
                                            </small>
                                            <div class="mt-1">
                                                <span class="badge bg-success">
                                                    <i class="fas fa-chart-line me-1"></i>
                                                    +<?php echo e(number_format($package->getProfit())); ?>đ (<?php echo e($package->getProfitMargin()); ?>%)
                                                </span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <?php
                                        $customerCount = $package->customerServices->count();
                                    ?>
                                    <?php if($customerCount > 0): ?>
                                        <span class="badge bg-primary">
                                            <i class="fas fa-users me-1"></i>
                                            <?php echo e($customerCount); ?> khách hàng
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-minus me-1"></i>
                                            Chưa có khách hàng
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center gap-2">
                                        <?php if($package->is_active): ?>
                                            <span class="badge bg-success">Hoạt động</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">Tạm dừng</span>
                                        <?php endif; ?>

                                        <form action="<?php echo e(route('admin.service-packages.toggle-status', $package)); ?>"
                                              method="POST" class="d-inline">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('PATCH'); ?>
                                            <button type="submit"
                                                    class="btn btn-sm <?php echo e($package->is_active ? 'btn-outline-warning' : 'btn-outline-success'); ?>"
                                                    title="<?php echo e($package->is_active ? 'Tạm dừng' : 'Kích hoạt'); ?>"
                                                    onclick="return confirm('Bạn có chắc muốn <?php echo e($package->is_active ? 'tạm dừng' : 'kích hoạt'); ?> gói dịch vụ này?')">
                                                <i class="fas <?php echo e($package->is_active ? 'fa-pause' : 'fa-play'); ?>"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                                <td class="table-action-column">
                                    <div class="btn-group" role="group">
                                        <a href="<?php echo e(route('admin.service-packages.show', $package)); ?>" 
                                           class="btn btn-sm btn-outline-info" 
                                           title="Xem chi tiết">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('admin.service-packages.edit', $package)); ?>" 
                                           class="btn btn-sm btn-outline-warning" 
                                           title="Chỉnh sửa">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form method="POST" 
                                              action="<?php echo e(route('admin.service-packages.destroy', $package)); ?>" 
                                              class="d-inline"
                                              onsubmit="return confirm('Bạn có chắc chắn muốn xóa gói dịch vụ này?')">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" 
                                                    class="btn btn-sm btn-outline-danger" 
                                                    title="Xóa">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                    </table>
                </div>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div class="text-muted">
                    Hiển thị <?php echo e($servicePackages->firstItem() ?? 0); ?> đến <?php echo e($servicePackages->lastItem() ?? 0); ?>

                    trong tổng số <?php echo e($servicePackages->total()); ?> gói dịch vụ
                </div>
                <div>
                    <?php echo e($servicePackages->appends(request()->query())->links()); ?>

                </div>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-box fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Không tìm thấy gói dịch vụ nào</h5>
                <?php if(request()->hasAny(['search', 'category_id', 'status', 'date_from', 'date_to'])): ?>
                    <p class="text-muted">Thử thay đổi bộ lọc hoặc <a href="<?php echo e(route('admin.service-packages.index')); ?>">xóa bộ lọc</a></p>
                <?php else: ?>
                    <p class="text-muted">Hãy <a href="<?php echo e(route('admin.service-packages.create')); ?>">thêm gói dịch vụ đầu tiên</a></p>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-validation for date inputs
    const dateFromInput = document.querySelector('input[name="date_from"]');
    const dateToInput = document.querySelector('input[name="date_to"]');
    
    if (dateFromInput && dateToInput) {
        dateFromInput.addEventListener('change', function() {
            if (this.value && dateToInput.value && this.value > dateToInput.value) {
                dateToInput.value = this.value;
            }
            dateToInput.min = this.value;
        });
        
        dateToInput.addEventListener('change', function() {
            if (this.value && dateFromInput.value && this.value < dateFromInput.value) {
                dateFromInput.value = this.value;
            }
            dateFromInput.max = this.value;
        });
    }
    
    // Real-time search with debounce
    const searchInput = document.querySelector('input[name="search"]');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const value = this.value.trim();
            
            if (value.length === 0 || value.length >= 3) {
                searchTimeout = setTimeout(() => {
                    // Auto-submit form when search has 3+ characters or is empty
                    if (value.length >= 3) {
                        document.querySelector('form').submit();
                    }
                }, 500);
            }
        });
    }
    
    // Enhanced status display
    const statusSelects = document.querySelectorAll('select[name="status"]');
    statusSelects.forEach(select => {
        select.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (selectedOption.value === 'active') {
                this.classList.remove('border-warning');
                this.classList.add('border-success');
            } else if (selectedOption.value === 'inactive') {
                this.classList.remove('border-success');
                this.classList.add('border-warning');
            } else {
                this.classList.remove('border-success', 'border-warning');
            }
        });
    });
    
    // Tooltip initialization
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Show filter summary
    const activeFilters = [];
    if (document.querySelector('input[name="search"]').value) {
        activeFilters.push('Tìm kiếm: ' + document.querySelector('input[name="search"]').value);
    }
    if (document.querySelector('select[name="category_id"]').value) {
        const categoryText = document.querySelector('select[name="category_id"] option:checked').text;
        activeFilters.push('Danh mục: ' + categoryText);
    }
    if (document.querySelector('select[name="status"]').value) {
        const statusText = document.querySelector('select[name="status"] option:checked').text.trim();
        activeFilters.push('Trạng thái: ' + statusText);
    }
    if (document.querySelector('input[name="date_from"]').value) {
        activeFilters.push('Từ ngày: ' + document.querySelector('input[name="date_from"]').value);
    }
    if (document.querySelector('input[name="date_to"]').value) {
        activeFilters.push('Đến ngày: ' + document.querySelector('input[name="date_to"]').value);
    }
    
    if (activeFilters.length > 0) {
        console.log('Bộ lọc đang áp dụng:', activeFilters.join(', '));
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\truycuuthongtin\resources\views/admin/service-packages/index.blade.php ENDPATH**/ ?>