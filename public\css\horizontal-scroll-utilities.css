/* ========================================
   HORIZONTAL SCROLL UTILITIES
   Tiện ích cuộn ngang cho tất cả các trang
   ======================================== */

/* Global horizontal scroll for all pages */
.main-content {
    overflow-x: auto !important;
    overflow-y: visible !important;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* Content area horizontal scroll */
.content-area {
    overflow-x: auto !important;
    overflow-y: visible !important;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    min-width: 100%;
}

/* Base horizontal scroll container */
.horizontal-scroll-container {
    width: 100%;
    max-width: 100%;
    overflow-x: auto !important;
    overflow-y: visible !important;
    position: relative;
    border-radius: 0.375rem;
    /* Smooth scrolling */
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* Enhanced table responsive with horizontal scroll */
.table-responsive-enhanced {
    width: 100%;
    max-width: 100%;
    overflow-x: auto !important;
    overflow-y: visible !important;
    position: relative;
    border-radius: 0.375rem;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    /* Ensure minimum height for scrollbar visibility */
    min-height: 50px;
}

/* Standard Bootstrap table-responsive override */
.table-responsive {
    overflow-x: auto !important;
    overflow-y: visible !important;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    border-radius: 0.375rem;
}

/* Table with fixed layout for consistent column widths */
.table-fixed-layout {
    table-layout: fixed;
    width: 100%;
    min-width: 100%;
    border-collapse: collapse;
}

/* Ensure action columns are always accessible */
.table-action-column {
    min-width: 160px;
    white-space: nowrap;
    text-align: center;
    position: relative;
}

/* Sticky action column (optional - for critical actions) */
.table-action-column-sticky {
    position: sticky;
    right: 0;
    background-color: white;
    z-index: 10;
    box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1);
    min-width: 160px;
    white-space: nowrap;
    text-align: center;
}

/* Dark theme support for sticky columns */
.dark-theme .table-action-column-sticky {
    background-color: #1a1a1a;
    box-shadow: -2px 0 4px rgba(255, 255, 255, 0.1);
}

/* ========================================
   SCROLLBAR STYLING
   ======================================== */

/* Modern scrollbar for all horizontal scroll containers */
.main-content::-webkit-scrollbar,
.content-area::-webkit-scrollbar,
.horizontal-scroll-container::-webkit-scrollbar,
.table-responsive-enhanced::-webkit-scrollbar,
.table-responsive::-webkit-scrollbar,
.card::-webkit-scrollbar,
.card-body::-webkit-scrollbar,
.row::-webkit-scrollbar,
.btn-group::-webkit-scrollbar {
    height: 8px;
    background: transparent;
}

.main-content::-webkit-scrollbar-track,
.content-area::-webkit-scrollbar-track,
.horizontal-scroll-container::-webkit-scrollbar-track,
.table-responsive-enhanced::-webkit-scrollbar-track,
.table-responsive::-webkit-scrollbar-track,
.card::-webkit-scrollbar-track,
.card-body::-webkit-scrollbar-track,
.row::-webkit-scrollbar-track,
.btn-group::-webkit-scrollbar-track {
    background: #f8fafc;
    border-radius: 4px;
    margin: 0 4px;
}

.main-content::-webkit-scrollbar-thumb,
.content-area::-webkit-scrollbar-thumb,
.horizontal-scroll-container::-webkit-scrollbar-thumb,
.table-responsive-enhanced::-webkit-scrollbar-thumb,
.table-responsive::-webkit-scrollbar-thumb,
.card::-webkit-scrollbar-thumb,
.card-body::-webkit-scrollbar-thumb,
.row::-webkit-scrollbar-thumb,
.btn-group::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.main-content::-webkit-scrollbar-thumb:hover,
.content-area::-webkit-scrollbar-thumb:hover,
.horizontal-scroll-container::-webkit-scrollbar-thumb:hover,
.table-responsive-enhanced::-webkit-scrollbar-thumb:hover,
.table-responsive::-webkit-scrollbar-thumb:hover,
.card::-webkit-scrollbar-thumb:hover,
.card-body::-webkit-scrollbar-thumb:hover,
.row::-webkit-scrollbar-thumb:hover,
.btn-group::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

.main-content::-webkit-scrollbar-thumb:active,
.content-area::-webkit-scrollbar-thumb:active,
.horizontal-scroll-container::-webkit-scrollbar-thumb:active,
.table-responsive-enhanced::-webkit-scrollbar-thumb:active,
.table-responsive::-webkit-scrollbar-thumb:active,
.card::-webkit-scrollbar-thumb:active,
.card-body::-webkit-scrollbar-thumb:active,
.row::-webkit-scrollbar-thumb:active,
.btn-group::-webkit-scrollbar-thumb:active {
    background: #64748b;
}

/* Firefox scrollbar styling */
.main-content,
.content-area,
.horizontal-scroll-container,
.table-responsive-enhanced,
.table-responsive,
.card,
.card-body,
.row,
.btn-group {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f8fafc;
}

/* ========================================
   CONTENT OVERFLOW UTILITIES
   ======================================== */

/* Horizontal scroll for card content */
.card-horizontal-scroll {
    overflow-x: auto !important;
    overflow-y: visible !important;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* All cards should have horizontal scroll */
.card {
    overflow-x: auto !important;
    overflow-y: visible !important;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

.card-body {
    overflow-x: auto !important;
    overflow-y: visible !important;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    min-width: 100%;
}

/* Horizontal scroll for dashboard widgets */
.widget-horizontal-scroll {
    overflow-x: auto !important;
    overflow-y: visible !important;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    min-height: 200px;
}

/* Dashboard rows and columns */
.row {
    overflow-x: auto !important;
    overflow-y: visible !important;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* Calendar horizontal scroll */
.calendar-horizontal-scroll {
    overflow-x: auto !important;
    overflow-y: visible !important;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    min-width: 100%;
}

/* Form horizontal scroll */
.form-horizontal-scroll {
    overflow-x: auto !important;
    overflow-y: visible !important;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* Button groups horizontal scroll */
.btn-group {
    overflow-x: auto !important;
    overflow-y: visible !important;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    white-space: nowrap;
}

/* Stats cards container */
.stats-container {
    overflow-x: auto !important;
    overflow-y: visible !important;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* ========================================
   RESPONSIVE BEHAVIOR
   ======================================== */

/* Ensure horizontal scroll works on all screen sizes */
@media (max-width: 1920px) {
    .main-content,
    .content-area,
    .horizontal-scroll-container,
    .table-responsive-enhanced,
    .table-responsive,
    .card,
    .card-body,
    .row {
        max-width: calc(100vw - 260px) !important; /* Account for sidebar */
        overflow-x: auto !important;
    }
}

@media (max-width: 1600px) {
    .main-content,
    .content-area,
    .horizontal-scroll-container,
    .table-responsive-enhanced,
    .table-responsive,
    .card,
    .card-body,
    .row {
        max-width: calc(100vw - 250px) !important;
        overflow-x: auto !important;
    }

    .table-action-column,
    .table-action-column-sticky {
        min-width: 140px;
    }
}

@media (max-width: 1200px) {
    .main-content,
    .content-area,
    .horizontal-scroll-container,
    .table-responsive-enhanced,
    .table-responsive,
    .card,
    .card-body,
    .row {
        max-width: calc(100vw - 240px) !important;
        overflow-x: auto !important;
    }

    .table-action-column,
    .table-action-column-sticky {
        min-width: 120px;
    }
}

@media (max-width: 1024px) {
    .main-content,
    .content-area,
    .horizontal-scroll-container,
    .table-responsive-enhanced,
    .table-responsive,
    .card,
    .card-body,
    .row {
        max-width: calc(100vw - 20px) !important; /* Mobile without sidebar */
        overflow-x: auto !important;
    }
}

@media (max-width: 768px) {
    .main-content,
    .content-area,
    .horizontal-scroll-container,
    .table-responsive-enhanced,
    .table-responsive,
    .card,
    .card-body,
    .row {
        max-width: calc(100vw - 15px) !important;
        overflow-x: auto !important;
    }

    .table-action-column,
    .table-action-column-sticky {
        min-width: 100px;
    }

    /* Larger scrollbar on mobile for easier interaction */
    .horizontal-scroll-container::-webkit-scrollbar,
    .table-responsive-enhanced::-webkit-scrollbar,
    .table-responsive::-webkit-scrollbar,
    .card::-webkit-scrollbar,
    .main-content::-webkit-scrollbar,
    .content-area::-webkit-scrollbar {
        height: 12px;
    }
}

/* ========================================
   UTILITY CLASSES
   ======================================== */

/* Force horizontal scroll always visible */
.force-horizontal-scroll {
    overflow-x: scroll !important;
    overflow-y: visible !important;
}

/* Hide horizontal scroll when not needed */
.auto-horizontal-scroll {
    overflow-x: auto !important;
    overflow-y: visible !important;
}

/* Prevent horizontal scroll */
.no-horizontal-scroll {
    overflow-x: hidden !important;
}

/* Smooth scroll behavior */
.smooth-scroll {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* Global page wrapper horizontal scroll */
.page-wrapper {
    overflow-x: auto !important;
    overflow-y: visible !important;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* Container fluid horizontal scroll */
.container-fluid {
    overflow-x: auto !important;
    overflow-y: visible !important;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* All Bootstrap columns */
[class*="col-"] {
    overflow-x: auto !important;
    overflow-y: visible !important;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* Modal content horizontal scroll */
.modal-content {
    overflow-x: auto !important;
    overflow-y: visible !important;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* Dropdown menu horizontal scroll */
.dropdown-menu {
    overflow-x: auto !important;
    overflow-y: visible !important;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* ========================================
   TABLE SPECIFIC UTILITIES
   ======================================== */

/* Minimum table width to ensure all columns are visible */
.table-min-width-full {
    min-width: 100%;
}

.table-min-width-large {
    min-width: 1200px;
}

.table-min-width-extra-large {
    min-width: 1600px;
}

/* Text truncation for table cells */
.table-cell-truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
}

/* No text truncation for important columns */
.table-cell-no-truncate {
    white-space: normal;
    word-wrap: break-word;
    overflow: visible;
}

/* ========================================
   ACCESSIBILITY IMPROVEMENTS
   ======================================== */

/* Focus indicators for keyboard navigation */
.main-content:focus,
.content-area:focus,
.horizontal-scroll-container:focus,
.table-responsive-enhanced:focus,
.table-responsive:focus,
.card:focus,
.card-body:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .main-content::-webkit-scrollbar-thumb,
    .content-area::-webkit-scrollbar-thumb,
    .horizontal-scroll-container::-webkit-scrollbar-thumb,
    .table-responsive-enhanced::-webkit-scrollbar-thumb,
    .table-responsive::-webkit-scrollbar-thumb,
    .card::-webkit-scrollbar-thumb,
    .card-body::-webkit-scrollbar-thumb {
        background: #000000;
    }

    .main-content::-webkit-scrollbar-track,
    .content-area::-webkit-scrollbar-track,
    .horizontal-scroll-container::-webkit-scrollbar-track,
    .table-responsive-enhanced::-webkit-scrollbar-track,
    .table-responsive::-webkit-scrollbar-track,
    .card::-webkit-scrollbar-track,
    .card-body::-webkit-scrollbar-track {
        background: #ffffff;
        border: 1px solid #000000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .main-content,
    .content-area,
    .horizontal-scroll-container,
    .table-responsive-enhanced,
    .table-responsive,
    .card,
    .card-body,
    .smooth-scroll {
        scroll-behavior: auto;
    }
}

/* ========================================
   PRINT STYLES
   ======================================== */

@media print {
    .main-content,
    .content-area,
    .horizontal-scroll-container,
    .table-responsive-enhanced,
    .table-responsive,
    .card,
    .card-body,
    .row,
    .container-fluid,
    [class*="col-"] {
        overflow: visible !important;
        max-width: none !important;
        width: auto !important;
    }

    .table-action-column-sticky {
        position: static !important;
        box-shadow: none !important;
    }
}

/* ========================================
   ADDITIONAL GLOBAL OVERRIDES
   ======================================== */

/* Ensure all elements can scroll horizontally when needed */
* {
    box-sizing: border-box;
}

/* Body and HTML horizontal scroll */
html,
body {
    overflow-x: auto !important;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* Wrapper elements */
.wrapper,
.main-wrapper,
.page-content,
.content-wrapper {
    overflow-x: auto !important;
    overflow-y: visible !important;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}
