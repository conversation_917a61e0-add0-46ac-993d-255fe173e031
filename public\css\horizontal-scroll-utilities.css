/* ========================================
   HORIZONTAL SCROLL UTILITIES
   Tiện ích cuộn ngang cho tất cả các trang
   ======================================== */

/* Base horizontal scroll container */
.horizontal-scroll-container {
    width: 100%;
    max-width: 100%;
    overflow-x: auto;
    overflow-y: visible;
    position: relative;
    border-radius: 0.375rem;
    /* Smooth scrolling */
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* Enhanced table responsive with horizontal scroll */
.table-responsive-enhanced {
    width: 100%;
    max-width: 100%;
    overflow-x: auto;
    overflow-y: visible;
    position: relative;
    border-radius: 0.375rem;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    /* Ensure minimum height for scrollbar visibility */
    min-height: 50px;
}

/* Table with fixed layout for consistent column widths */
.table-fixed-layout {
    table-layout: fixed;
    width: 100%;
    min-width: 100%;
    border-collapse: collapse;
}

/* Ensure action columns are always accessible */
.table-action-column {
    min-width: 160px;
    white-space: nowrap;
    text-align: center;
    position: relative;
}

/* Sticky action column (optional - for critical actions) */
.table-action-column-sticky {
    position: sticky;
    right: 0;
    background-color: white;
    z-index: 10;
    box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1);
    min-width: 160px;
    white-space: nowrap;
    text-align: center;
}

/* Dark theme support for sticky columns */
.dark-theme .table-action-column-sticky {
    background-color: #1a1a1a;
    box-shadow: -2px 0 4px rgba(255, 255, 255, 0.1);
}

/* ========================================
   SCROLLBAR STYLING
   ======================================== */

/* Modern scrollbar for horizontal scroll containers */
.horizontal-scroll-container::-webkit-scrollbar,
.table-responsive-enhanced::-webkit-scrollbar {
    height: 8px;
    background: transparent;
}

.horizontal-scroll-container::-webkit-scrollbar-track,
.table-responsive-enhanced::-webkit-scrollbar-track {
    background: #f8fafc;
    border-radius: 4px;
    margin: 0 4px;
}

.horizontal-scroll-container::-webkit-scrollbar-thumb,
.table-responsive-enhanced::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.horizontal-scroll-container::-webkit-scrollbar-thumb:hover,
.table-responsive-enhanced::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

.horizontal-scroll-container::-webkit-scrollbar-thumb:active,
.table-responsive-enhanced::-webkit-scrollbar-thumb:active {
    background: #64748b;
}

/* Firefox scrollbar styling */
.horizontal-scroll-container,
.table-responsive-enhanced {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f8fafc;
}

/* ========================================
   CONTENT OVERFLOW UTILITIES
   ======================================== */

/* Horizontal scroll for card content */
.card-horizontal-scroll {
    overflow-x: auto;
    overflow-y: visible;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* Horizontal scroll for dashboard widgets */
.widget-horizontal-scroll {
    overflow-x: auto;
    overflow-y: visible;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    min-height: 200px;
}

/* Calendar horizontal scroll */
.calendar-horizontal-scroll {
    overflow-x: auto;
    overflow-y: visible;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    min-width: 100%;
}

/* ========================================
   RESPONSIVE BEHAVIOR
   ======================================== */

/* Ensure horizontal scroll works on all screen sizes */
@media (max-width: 1920px) {
    .horizontal-scroll-container,
    .table-responsive-enhanced {
        max-width: calc(100vw - 30px);
    }
}

@media (max-width: 1600px) {
    .horizontal-scroll-container,
    .table-responsive-enhanced {
        max-width: calc(100vw - 25px);
    }
    
    .table-action-column,
    .table-action-column-sticky {
        min-width: 140px;
    }
}

@media (max-width: 1200px) {
    .horizontal-scroll-container,
    .table-responsive-enhanced {
        max-width: calc(100vw - 20px);
    }
    
    .table-action-column,
    .table-action-column-sticky {
        min-width: 120px;
    }
}

@media (max-width: 768px) {
    .horizontal-scroll-container,
    .table-responsive-enhanced {
        max-width: calc(100vw - 15px);
    }
    
    .table-action-column,
    .table-action-column-sticky {
        min-width: 100px;
    }
    
    /* Larger scrollbar on mobile for easier interaction */
    .horizontal-scroll-container::-webkit-scrollbar,
    .table-responsive-enhanced::-webkit-scrollbar {
        height: 12px;
    }
}

/* ========================================
   UTILITY CLASSES
   ======================================== */

/* Force horizontal scroll always visible */
.force-horizontal-scroll {
    overflow-x: scroll !important;
}

/* Hide horizontal scroll when not needed */
.auto-horizontal-scroll {
    overflow-x: auto !important;
}

/* Prevent horizontal scroll */
.no-horizontal-scroll {
    overflow-x: hidden !important;
}

/* Smooth scroll behavior */
.smooth-scroll {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* ========================================
   TABLE SPECIFIC UTILITIES
   ======================================== */

/* Minimum table width to ensure all columns are visible */
.table-min-width-full {
    min-width: 100%;
}

.table-min-width-large {
    min-width: 1200px;
}

.table-min-width-extra-large {
    min-width: 1600px;
}

/* Text truncation for table cells */
.table-cell-truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
}

/* No text truncation for important columns */
.table-cell-no-truncate {
    white-space: normal;
    word-wrap: break-word;
    overflow: visible;
}

/* ========================================
   ACCESSIBILITY IMPROVEMENTS
   ======================================== */

/* Focus indicators for keyboard navigation */
.horizontal-scroll-container:focus,
.table-responsive-enhanced:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .horizontal-scroll-container::-webkit-scrollbar-thumb,
    .table-responsive-enhanced::-webkit-scrollbar-thumb {
        background: #000000;
    }
    
    .horizontal-scroll-container::-webkit-scrollbar-track,
    .table-responsive-enhanced::-webkit-scrollbar-track {
        background: #ffffff;
        border: 1px solid #000000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .horizontal-scroll-container,
    .table-responsive-enhanced,
    .smooth-scroll {
        scroll-behavior: auto;
    }
}

/* ========================================
   PRINT STYLES
   ======================================== */

@media print {
    .horizontal-scroll-container,
    .table-responsive-enhanced {
        overflow: visible !important;
        max-width: none !important;
    }
    
    .table-action-column-sticky {
        position: static !important;
        box-shadow: none !important;
    }
}
