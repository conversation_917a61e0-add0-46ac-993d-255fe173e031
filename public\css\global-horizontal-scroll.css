/* ========================================
   GLOBAL HORIZONTAL SCROLL OVERRIDE
   Đảm bảo tất cả các trang có thể cuộn ngang
   ======================================== */

/* Base HTML and Body */
html, body {
    overflow-x: auto !important;
    overflow-y: visible !important;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    box-sizing: border-box;
}

/* All major container elements */
.main-content,
.content-area,
.container,
.container-fluid,
.container-sm,
.container-md,
.container-lg,
.container-xl,
.container-xxl {
    overflow-x: auto !important;
    overflow-y: visible !important;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* All Bootstrap grid elements */
.row,
[class*="col-"],
.col,
.col-auto,
.col-1, .col-2, .col-3, .col-4, .col-5, .col-6,
.col-7, .col-8, .col-9, .col-10, .col-11, .col-12,
.col-sm, .col-sm-auto, .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6,
.col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12,
.col-md, .col-md-auto, .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6,
.col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12,
.col-lg, .col-lg-auto, .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6,
.col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12,
.col-xl, .col-xl-auto, .col-xl-1, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6,
.col-xl-7, .col-xl-8, .col-xl-9, .col-xl-10, .col-xl-11, .col-xl-12,
.col-xxl, .col-xxl-auto, .col-xxl-1, .col-xxl-2, .col-xxl-3, .col-xxl-4, .col-xxl-5, .col-xxl-6,
.col-xxl-7, .col-xxl-8, .col-xxl-9, .col-xxl-10, .col-xxl-11, .col-xxl-12 {
    overflow-x: auto !important;
    overflow-y: visible !important;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* All card components */
.card,
.card-body,
.card-header,
.card-footer,
.card-group,
.card-deck,
.card-columns {
    overflow-x: auto !important;
    overflow-y: visible !important;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* All table components */
.table-responsive,
.table-responsive-sm,
.table-responsive-md,
.table-responsive-lg,
.table-responsive-xl,
.table-responsive-xxl,
.table-responsive-enhanced,
.table-container,
.table-wrapper {
    overflow-x: auto !important;
    overflow-y: visible !important;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* Form components */
.form-group,
.form-row,
.form-control,
.input-group,
.form-floating {
    overflow-x: auto !important;
    overflow-y: visible !important;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* Button components */
.btn-group,
.btn-toolbar,
.button-group {
    overflow-x: auto !important;
    overflow-y: visible !important;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    white-space: nowrap;
}

/* Navigation components */
.navbar,
.nav,
.nav-tabs,
.nav-pills,
.breadcrumb,
.pagination {
    overflow-x: auto !important;
    overflow-y: visible !important;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* Modal components */
.modal,
.modal-dialog,
.modal-content,
.modal-body,
.modal-header,
.modal-footer {
    overflow-x: auto !important;
    overflow-y: visible !important;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* Dropdown components */
.dropdown,
.dropdown-menu,
.dropdown-item {
    overflow-x: auto !important;
    overflow-y: visible !important;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* Alert components */
.alert,
.toast,
.toast-container {
    overflow-x: auto !important;
    overflow-y: visible !important;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* List components */
.list-group,
.list-group-item {
    overflow-x: auto !important;
    overflow-y: visible !important;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* Dashboard specific components */
.dashboard,
.widget,
.stats-card,
.chart-container,
.calendar-container {
    overflow-x: auto !important;
    overflow-y: visible !important;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* Content scheduler specific */
.calendar-horizontal-scroll,
.content-scheduler,
.post-list,
.scheduler-container {
    overflow-x: auto !important;
    overflow-y: visible !important;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* Custom application components */
.horizontal-scroll-container,
.page-wrapper,
.content-wrapper,
.main-wrapper,
.app-container {
    overflow-x: auto !important;
    overflow-y: visible !important;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* Sidebar adjustments for horizontal scroll */
.sidebar {
    position: fixed;
    z-index: 1000;
}

/* Main content with sidebar */
@media (min-width: 1025px) {
    .main-content {
        margin-left: 240px;
        max-width: calc(100vw - 240px) !important;
    }
}

@media (max-width: 1024px) {
    .main-content {
        margin-left: 0;
        max-width: 100vw !important;
    }
}

/* Responsive adjustments */
@media (max-width: 1920px) {
    .main-content,
    .content-area,
    .container-fluid {
        max-width: calc(100vw - 260px) !important;
    }
}

@media (max-width: 1600px) {
    .main-content,
    .content-area,
    .container-fluid {
        max-width: calc(100vw - 250px) !important;
    }
}

@media (max-width: 1200px) {
    .main-content,
    .content-area,
    .container-fluid {
        max-width: calc(100vw - 240px) !important;
    }
}

@media (max-width: 1024px) {
    .main-content,
    .content-area,
    .container-fluid {
        max-width: calc(100vw - 20px) !important;
    }
}

@media (max-width: 768px) {
    .main-content,
    .content-area,
    .container-fluid {
        max-width: calc(100vw - 15px) !important;
    }
}

/* Scrollbar styling for all elements */
*::-webkit-scrollbar {
    height: 8px;
    width: 8px;
    background: transparent;
}

*::-webkit-scrollbar-track {
    background: #f8fafc;
    border-radius: 4px;
    margin: 0 4px;
}

*::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

*::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

*::-webkit-scrollbar-thumb:active {
    background: #64748b;
}

/* Firefox scrollbar styling */
* {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f8fafc;
}

/* Mobile scrollbar adjustments */
@media (max-width: 768px) {
    *::-webkit-scrollbar {
        height: 12px;
        width: 12px;
    }
}

/* Print styles */
@media print {
    * {
        overflow: visible !important;
        max-width: none !important;
        width: auto !important;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    *::-webkit-scrollbar-thumb {
        background: #000000;
    }
    
    *::-webkit-scrollbar-track {
        background: #ffffff;
        border: 1px solid #000000;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    * {
        scroll-behavior: auto !important;
    }
}
