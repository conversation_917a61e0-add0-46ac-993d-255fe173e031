<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'Admin Panel') - <PERSON><PERSON><PERSON>n l<PERSON> t<PERSON>n số</title>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom Responsive CSS -->
    <link href="{{ asset('css/responsive-tables.css') }}" rel="stylesheet">
    <!-- Customer Table Fix CSS -->
    <link href="{{ asset('css/customer-table-fix.css') }}" rel="stylesheet">
    <!-- Layout Optimization CSS -->
    <link href="{{ asset('css/layout-optimization.css') }}" rel="stylesheet">
    <!-- Table Fix Override CSS -->
    <link href="{{ asset('css/table-fix-override.css') }}" rel="stylesheet">
    <!-- Horizontal Scroll Utilities CSS -->
    <link href="{{ asset('css/horizontal-scroll-utilities.css') }}" rel="stylesheet">

    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #10b981 0%, #059669 100%);
            --warning-gradient: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            --danger-gradient: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            --info-gradient: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            --dark-gradient: linear-gradient(135deg, #374151 0%, #1f2937 100%);
            --light-bg: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            --card-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
            --card-shadow-hover: 0 20px 40px rgba(0, 0, 0, 0.12);
            --border-radius: 16px;
            --border-radius-lg: 20px;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--light-bg);
            min-height: 100vh;
            margin: 0;
            font-size: 15px;
            line-height: 1.6;
            color: #374151;
        }

        /* Performance Optimization - Disable heavy animations */
        *,
        *::before,
        *::after {
            animation-duration: 0.01ms !important;
            animation-delay: -1ms !important;
            transition-duration: 0.01ms !important;
            scroll-behavior: auto !important;
        }

        /* Keep only essential smooth transitions */
        .nav-link,
        .btn,
        .card {
            transition: background-color 0.15s ease, color 0.15s ease, border-color 0.15s ease !important;
        }

        .nav-link:hover,
        .btn:hover {
            transition: background-color 0.1s ease, color 0.1s ease !important;
        }

        /* Disable backdrop-filter for better performance */
        .main-header,
        .card {
            backdrop-filter: none !important;
        }

        /* Sidebar Styling */
        .sidebar {
            min-height: 100vh;
            background: var(--primary-gradient);
            position: fixed;
            left: 0;
            top: 0;
            width: 240px;
            z-index: 1000;
            overflow-y: auto;
        }

        .sidebar .brand {
            padding: 1.5rem 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }

        .sidebar .brand h4 {
            color: white;
            font-weight: 800;
            font-size: 1.4rem;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.85);
            padding: 0.8rem 1rem;
            border-radius: var(--border-radius);
            margin: 0.2rem 0.75rem;
            transition: all 0.2s ease;
            position: relative;
            font-weight: 500;
            font-size: 0.95rem;
        }

        .sidebar .nav-link:hover {
            color: white;
            background: rgba(255, 255, 255, 0.15);
        }

        .sidebar .nav-link.active {
            color: white;
            background: rgba(255, 255, 255, 0.2);
        }

        .sidebar .nav-link i {
            width: 20px;
            text-align: center;
        }

        /* Main Content Styling */
        .main-content {
            margin-left: 240px;
            background: var(--light-bg);
            min-height: 100vh;
        }

        .main-header {
            background: rgba(255, 255, 255, 0.95);
            padding: 1rem 1.5rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .page-title {
            font-size: 1.5rem;
            font-weight: 800;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0;
        }

        .content-area {
            padding: 1rem;
        }

        /* Card Components */
        .card {
            border: none;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--card-shadow);
            background: rgba(255, 255, 255, 0.95);
            transition: none;
            overflow: hidden;
        }

        .card:hover {
            box-shadow: var(--card-shadow);
        }

        .card-header {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            padding: 1rem 1.5rem;
            font-weight: 600;
            font-size: 1rem;
        }

        .card-body {
            padding: 1.5rem;
        }

        .card-stats {
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .card-stats::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
        }

        .card-stats .stats-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 2rem;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .card-stats .stats-icon::after {
            content: '';
            position: absolute;
            inset: 0;
            border-radius: 50%;
            background: inherit;
            filter: blur(20px);
            opacity: 0.3;
            z-index: -1;
        }

        .card-stats .stats-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: #1f2937;
            margin: 0;
        }

        .card-stats .stats-label {
            color: #6b7280;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.85rem;
        }

        /* Button Components */
        .btn {
            border-radius: var(--border-radius);
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
            border: none;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: var(--primary-gradient);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-success {
            background: var(--success-gradient);
            color: white;
        }

        .btn-warning {
            background: var(--warning-gradient);
            color: white;
        }

        .btn-danger {
            background: var(--danger-gradient);
            color: white;
        }

        .btn-info {
            background: var(--info-gradient);
            color: white;
        }

        .btn-outline-primary {
            border: 2px solid #667eea;
            color: #667eea;
            background: transparent;
        }

        .btn-outline-primary:hover {
            background: var(--primary-gradient);
            border-color: transparent;
            color: white;
        }

        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }

        .btn-lg {
            padding: 1rem 2rem;
            font-size: 1.125rem;
        }

        /* Table Components */
        .table-container {
            border-radius: var(--border-radius-lg);
            overflow: hidden;
            box-shadow: var(--card-shadow);
            background: white;
        }

        .table-responsive {
            max-width: 100%;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            border-radius: var(--border-radius);
        }

        /* Tối ưu kích thước bảng */
        .table {
            font-size: 14px;
            margin-bottom: 0;
            min-width: 1200px;
            /* Đảm bảo bảng không bị méo */
        }

        .table th,
        .table td {
            padding: 8px 6px;
            vertical-align: middle;
            white-space: nowrap;
        }

        .table th {
            font-size: 13px;
            font-weight: 600;
            background: var(--dark-gradient) !important;
            border: none;
        }

        /* Responsive cho các cột */
        @media (max-width: 1400px) {
            .table .d-none-lg {
                display: none !important;
            }

            .table {
                min-width: 1000px;
            }
        }

        @media (max-width: 1200px) {
            .table .d-none-xl {
                display: none !important;
            }

            .table {
                min-width: 900px;
            }

            .table th,
            .table td {
                padding: 6px 4px;
                font-size: 13px;
            }
        }

        @media (max-width: 992px) {
            .table .d-none-md {
                display: none !important;
            }

            .table {
                min-width: 700px;
            }

            .table th,
            .table td {
                padding: 5px 3px;
                font-size: 12px;
            }
        }

        /* Container responsive */
        .container-fluid {
            max-width: 100%;
            padding-left: 15px;
            padding-right: 15px;
        }

        @media (min-width: 1200px) {
            .container-fluid {
                max-width: 1400px;
                margin: 0 auto;
            }
        }

        /* Card responsive */
        .card {
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            margin-bottom: 20px;
            overflow: hidden;
        }

        /* Badge responsive */
        .badge {
            font-size: 11px;
            padding: 4px 8px;
            white-space: nowrap;
        }

        /* Button group responsive */
        .btn-group .btn {
            padding: 4px 8px;
            font-size: 12px;
        }

        /* Stats cards responsive */
        .row .col-md-2,
        .row .col-md-3 {
            margin-bottom: 15px;
        }

        @media (max-width: 768px) {
            .row .col-md-2,
            .row .col-md-3 {
                flex: 0 0 50%;
                max-width: 50%;
            }
        }

        /* Form controls responsive */
        .form-control,
        .form-select {
            font-size: 14px;
            padding: 8px 12px;
        }

        /* Password field styles */
        .password-field {
            font-family: monospace;
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            border: 1px solid #e9ecef;
        }

        /* Dropdown menu responsive */
        .dropdown-menu {
            font-size: 13px;
            min-width: 180px;
        }

        .dropdown-item {
            padding: 6px 12px;
        }

        /* Thống kê cards */
        .card.bg-primary,
        .card.bg-success,
        .card.bg-info,
        .card.bg-warning,
        .card.bg-danger {
            background: var(--primary-gradient) !important;
            border: none;
        }

        .card.bg-success {
            background: var(--success-gradient) !important;
        }

        .card.bg-info {
            background: var(--info-gradient) !important;
        }

        .card.bg-warning {
            background: var(--warning-gradient) !important;
        }

        .card.bg-danger {
            background: var(--danger-gradient) !important;
        }

        /* Smooth scrolling cho table responsive */
        .table-responsive::-webkit-scrollbar {
            height: 8px;
        }

        .table-responsive::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .table-responsive::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .table-responsive::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* Toast Notifications */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
        }

        .toast-modern {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: none;
            border-radius: var(--border-radius);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            border-left: 4px solid var(--primary-gradient);
        }

        .toast-success {
            border-left-color: #10b981;
        }

        .toast-error {
            border-left-color: #ef4444;
        }

        .toast-warning {
            border-left-color: #f59e0b;
        }

        .toast-info {
            border-left-color: #3b82f6;
        }

        /* Data Tables Enhancement */
        .dataTables_wrapper {
            padding: 0;
        }

        .dataTables_filter {
            margin-bottom: 1rem;
        }

        .dataTables_filter input {
            border: 2px solid rgba(0, 0, 0, 0.1);
            border-radius: var(--border-radius);
            padding: 0.5rem 1rem;
            margin-left: 0.5rem;
        }

        .dataTables_length select {
            border: 2px solid rgba(0, 0, 0, 0.1);
            border-radius: var(--border-radius);
            padding: 0.5rem;
            margin: 0 0.5rem;
        }

        /* Print Styles */
        @media print {

            .sidebar,
            .main-header,
            .btn,
            .form-actions {
                display: none !important;
            }

            .main-content {
                margin-left: 0 !important;
            }

            .card {
                box-shadow: none !important;
                border: 1px solid #ddd !important;
            }

            .table {
                font-size: 12px;
            }

            .page-break {
                page-break-before: always;
            }
        }
    </style>

    @yield('styles')
</head>

<body>
    <div class="container-fluid p-0">
        <div class="row g-0">
            <!-- Sidebar -->
            <div class="sidebar" id="sidebar">
                <div class="brand">
                    <h4>
                        <i class="fas fa-crown me-2"></i>
                        KienUnlocked
                    </h4>
                </div>

                <nav class="nav flex-column px-2 py-3">
                    <a class="nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}"
                        href="{{ route('admin.dashboard') }}">
                        <i class="fas fa-tachometer-alt me-3"></i>
                        Dashboard
                    </a>

                    <a class="nav-link {{ request()->routeIs('admin.customers.*') ? 'active' : '' }}"
                        href="{{ route('admin.customers.index') }}">
                        <i class="fas fa-users me-3"></i>
                        Khách hàng
                    </a>

                    <a class="nav-link {{ request()->routeIs('admin.leads.*') ? 'active' : '' }}"
                        href="{{ route('admin.leads.index') }}">
                        <i class="fas fa-user-plus me-3"></i>
                        Lead (KH tiềm năng)
                    </a>

                    <a class="nav-link {{ request()->routeIs('admin.service-packages.*') ? 'active' : '' }}"
                        href="{{ route('admin.service-packages.index') }}">
                        <i class="fas fa-box me-3"></i>
                        Gói dịch vụ
                    </a>

                    <a class="nav-link {{ request()->routeIs('admin.suppliers.*') ? 'active' : '' }}"
                        href="{{ route('admin.suppliers.index') }}">
                        <i class="fas fa-truck me-3"></i>
                        Nhà cung cấp
                    </a>

                    <a class="nav-link {{ request()->routeIs('admin.collaborators.*') ? 'active' : '' }}"
                        href="{{ route('admin.collaborators.index') }}">
                        <i class="fas fa-user-friends me-3"></i>
                        Cộng tác viên
                    </a>

                    <a class="nav-link {{ request()->routeIs('admin.customer-services.*') ? 'active' : '' }}"
                        href="{{ route('admin.customer-services.index') }}">
                        <i class="fas fa-link me-3"></i>
                        Dịch vụ khách hàng
                    </a>

                    <a class="nav-link {{ request()->routeIs('admin.reports.*') ? 'active' : '' }}"
                        href="{{ route('admin.reports.profit') }}">
                        <i class="fas fa-chart-line me-3"></i>
                        Báo cáo lợi nhuận
                    </a>

                    <hr class="text-white-50 mx-3">

                    <a class="nav-link {{ request()->routeIs('admin.content-scheduler.*') ? 'active' : '' }}"
                        href="{{ route('admin.content-scheduler.index') }}">
                        <i class="fas fa-calendar-alt me-3"></i>
                        Lịch đăng bài
                    </a>

                    <hr class="text-white-50 mx-3">

                    <a class="nav-link" href="{{ route('lookup.index') }}" target="_blank">
                        <i class="fas fa-search me-3"></i>
                        Trang tra cứu
                        <i class="fas fa-external-link-alt ms-auto"></i>
                    </a>
                </nav>
            </div>

            <!-- Main Content -->
            <div class="main-content">
                <!-- Header -->
                <div class="main-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <!-- Mobile menu toggle -->
                            <button class="btn btn-outline-primary d-lg-none me-3" type="button" id="sidebarToggle">
                                <i class="fas fa-bars"></i>
                            </button>
                            <h1 class="page-title">@yield('page-title', 'Dashboard')</h1>
                        </div>
                        <div class="d-flex align-items-center">
                            <div class="text-muted me-4 d-none d-md-block">
                                <i class="fas fa-calendar me-2"></i>
                                {{ now()->format('d/m/Y H:i') }}
                            </div>
                            <div class="dropdown">
                                <button class="btn btn-outline-primary dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-user me-2"></i>
                                    <span class="d-none d-sm-inline">{{ Auth::guard('admin')->user()->name ?? 'Admin' }}</span>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                    <li>
                                        <form method="POST" action="{{ route('admin.logout') }}" class="d-inline">
                                            @csrf
                                            <button type="submit" class="dropdown-item">
                                                <i class="fas fa-sign-out-alt me-2 text-danger"></i>
                                                Đăng xuất
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Content Area -->
                <div class="content-area">
                    <!-- Alerts -->
                    @if(session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        {{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    @endif

                    @if(session('error'))
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        {{ session('error') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    @endif

                    <!-- Main Content -->
                    @yield('content')
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Enhanced Mobile sidebar toggle with overlay
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebar = document.getElementById('sidebar');
            let overlay;

            // Create overlay element
            function createOverlay() {
                overlay = document.createElement('div');
                overlay.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(0,0,0,0.5);
                    z-index: 999;
                    opacity: 0;
                    transition: opacity 0.3s ease;
                    display: none;
                `;
                document.body.appendChild(overlay);
            }

            function showSidebar() {
                if (window.innerWidth <= 1024) {
                    sidebar.classList.add('show');
                    if (!overlay) createOverlay();
                    overlay.style.display = 'block';
                    overlay.style.opacity = '1';
                    document.body.style.overflow = 'hidden';
                }
            }

            function hideSidebar() {
                sidebar.classList.remove('show');
                if (overlay) {
                    overlay.style.opacity = '0';
                    overlay.style.display = 'none';
                    document.body.style.overflow = '';
                }
            }

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    if (sidebar.classList.contains('show')) {
                        hideSidebar();
                    } else {
                        showSidebar();
                    }
                });
            }

            // Close sidebar when clicking overlay
            document.addEventListener('click', function(e) {
                if (overlay && e.target === overlay) {
                    hideSidebar();
                }
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth > 1024) {
                    hideSidebar();
                }
            });

            // Simple alert auto-hide
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    if (alert.parentNode) {
                        const bsAlert = new bootstrap.Alert(alert);
                        bsAlert.close();
                    }
                }, 5000);
            });

            // Simple form submission
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    const submitBtn = form.querySelector('button[type="submit"], input[type="submit"]');
                    if (submitBtn && !submitBtn.disabled) {
                        submitBtn.disabled = true;

                        const originalText = submitBtn.innerHTML;
                        const icon = submitBtn.querySelector('i');

                        if (icon) {
                            icon.className = icon.className.replace(/fa-\S+/g, 'fa-spinner fa-spin');
                        }

                        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Đang xử lý...';

                        // Re-enable after timeout as fallback
                        setTimeout(() => {
                            submitBtn.disabled = false;
                            submitBtn.classList.remove('loading');
                            submitBtn.innerHTML = originalText;
                        }, 15000);
                    }
                });
            });

            // Remove button click animations
            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });
    </script>

    <!-- Page-specific scripts -->
    @yield('scripts')

    <!-- Action Buttons Fix Script -->
    <script src="{{ asset('js/action-buttons-fix.js') }}"></script>

    <!-- Push scripts stack -->
    @stack('scripts')
</body>

</html>