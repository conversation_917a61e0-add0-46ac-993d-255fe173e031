/* Service Packages Page Fixes */

/* <PERSON><PERSON><PERSON> b<PERSON><PERSON> nút thêm gói dịch vụ luôn hiển thị */
.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    border: none !important;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3) !important;
    transition: all 0.3s ease !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.btn-success:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4) !important;
}

/* <PERSON><PERSON><PERSON> b<PERSON><PERSON> c<PERSON>c nút thao tác luôn hiển thị */
.table-action-column {
    position: sticky !important;
    right: 0 !important;
    background: white !important;
    z-index: 10 !important;
    box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1) !important;
    min-width: 160px !important;
    max-width: 160px !important;
    width: 160px !important;
    display: table-cell !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.table-action-column .btn-group {
    display: flex !important;
    gap: 2px !important;
    justify-content: center !important;
    align-items: center !important;
    white-space: nowrap !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.table-action-column .btn {
    flex-shrink: 0 !important;
    min-width: 40px !important;
    height: 32px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Đảm bảo table có thể cuộn ngang */
.table-responsive {
    overflow-x: auto !important;
    overflow-y: visible !important;
}

.table {
    min-width: 1200px !important;
    margin-bottom: 0 !important;
}

/* Đảm bảo header cũng sticky */
.table thead th.table-action-column {
    position: sticky !important;
    right: 0 !important;
    background: #f8f9fa !important;
    z-index: 11 !important;
    box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1) !important;
    display: table-cell !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Force visibility for all action elements */
.table-action-column,
.table-action-column .btn-group,
.table-action-column .btn,
.table-action-column form,
.table-action-column a {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.table-action-column {
    display: table-cell !important;
}

/* Ensure buttons are properly styled */
.table-action-column .btn-info {
    background-color: #17a2b8 !important;
    border-color: #17a2b8 !important;
}

.table-action-column .btn-warning {
    background-color: #ffc107 !important;
    border-color: #ffc107 !important;
}

.table-action-column .btn-danger {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
    .table-action-column {
        min-width: 140px !important;
        max-width: 140px !important;
        width: 140px !important;
    }
}

@media (max-width: 768px) {
    .table-action-column {
        min-width: 120px !important;
        max-width: 120px !important;
        width: 120px !important;
    }
    
    .table-action-column .btn {
        min-width: 35px !important;
        height: 30px !important;
        font-size: 12px !important;
    }
}
